
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters
🛡️ เริ่มใช้งาน Model Protection System (min profit: $5,000)
🚫 เริ่มใช้งาน Training Prevention System
🔧 โหลดการตั้งค่าป้องกัน Overfitting

🛡️ ระบบป้องกันโมเดล: FLEXIBLE
   🟡 เกณฑ์ยืดหยุ่น - สมดุลระหว่างคุณภาพและการยอมรับ
   - Accuracy ≥ 45.0%
   - Win Rate ≥ 35.0%
   - Expectancy ≥ 10.0

💡 แนวคิดการบันทึกโมเดล:
   🆕 โมเดลแรก: บันทึกเสมอ (ตามเกณฑ์ของแต่ละโหมด)
   🔄 โมเดลถัดไป: เปรียบเทียบกับโมเดลก่อนหน้า

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM/Multi
   📁 Hyperparameters: LightGBM/Hyper_Multi

🏗️ Creating Multi-Model Architecture Directories:
📁 Exists: LightGBM/Data_Trained (Data Storage)
📁 Exists: LightGBM/Hyper_Multi (Hyperparameters)
📁 Exists: LightGBM/Multi_Time (Time Used Folder)
📁 Exists: LightGBM/Multi (Main Multi-Model)
📁 Exists: LightGBM/Multi/feature_importance (Feature Importance)
📁 Exists: LightGBM/Multi/individual_performance (Performance Analysis)
📁 Exists: LightGBM/Multi/models (Models Base)
📁 Exists: LightGBM/Multi/results (Results)
📁 Exists: LightGBM/Multi/thresholds (Thresholds)
📁 Exists: LightGBM/Multi/training_summaries (Training Summaries)

🎯 PARAMETER OPTIMIZATION RESULTS APPLIED:
======================================================================
📊 Source: USDJPY_M60 specific optimization
   Score: 56.05, Win Rate: 32.9%, Total Profit: $26,041
   Total Trades: 167, Expectancy: 155.93, Max Drawdown: $22,190

🔄 Updated Global Parameters:
   input_stop_loss_atr: 1.0 (1.25 → 1.0)
   input_take_profit: 2.0 (3.0 → 2.0)
   input_rsi_level_in: 35 (40 → 35)
   input_volume_spike: 1.25 (1.5 → 1.25)
   input_rsi_level_over: 70 (unchanged)
   input_rsi_level_out: 30 (30 → 35)
   input_pull_back: 0.5 (unchanged)
   input_initial_nbar_sl: 4 (unchanged)
======================================================================

🔄 Using Multi-Model Architecture
Model base path set to: D:\test_gold\LightGBM\Multi\models
Threshold base path set to: D:\test_gold\LightGBM\Multi
Feature importance path: D:\test_gold\LightGBM\Multi\feature_importance
Available scenarios: ['trend_following', 'counter_trend']
✅ Successfully imported functions from WebRequest_Server_06.py
🧪 Dynamic Parameter Loading Tester
==================================================
Select mode:
1. 🚀 Run all tests
2. 🎮 Interactive mode
Choice (1-2): 🧪 Dynamic Parameter Loading Test Suite
============================================================

============================================================
🧪 Running Test: Timeframe Conversion

🔄 Testing Timeframe Conversion
========================================
   30 → M30
   60 → M60
   240 → H4
   1440 → D1
   15 → M15
   5 → M5
✅ Timeframe Conversion: PASSED

============================================================
🧪 Running Test: Optimization File Loading

📁 Testing Optimization File Loading
========================================

🔍 Testing USDJPY M60:
✅ โหลดพารามิเตอร์จากไฟล์: USDJPY_M60_optimization_results.json
   ✅ Found optimization file
   📊 Parameters:
      input_stop_loss_atr: 1.0
      input_take_profit: 2.0
      input_rsi_level_in: 35
      input_volume_spike: 1.25
      input_rsi_level_over: 70
      input_rsi_level_out: 35
      input_pull_back: 0.45
      input_initial_nbar_sl: 4

🔍 Testing GOLD M30:
✅ โหลดพารามิเตอร์จากไฟล์: GOLD_M30_optimization_results.json
   ✅ Found optimization file
   📊 Parameters:
      input_stop_loss_atr: 1.2
      input_take_profit: 2.5
      input_rsi_level_in: 38
      input_volume_spike: 1.3
      input_rsi_level_over: 70
      input_rsi_level_out: 32
      input_pull_back: 0.48
      input_initial_nbar_sl: 5

🔍 Testing GOLD M60:
✅ โหลดพารามิเตอร์จากไฟล์: GOLD_M60_optimization_results.json
   ✅ Found optimization file
   📊 Parameters:
      input_stop_loss_atr: 1.1
      input_take_profit: 2.2
      input_rsi_level_in: 36
      input_volume_spike: 1.2
      input_rsi_level_over: 70
      input_rsi_level_out: 34
      input_pull_back: 0.46
      input_initial_nbar_sl: 4

🔍 Testing EURUSD M30:
✅ โหลดพารามิเตอร์จากไฟล์: EURUSD_M30_optimization_results.json
   ✅ Found optimization file
   📊 Parameters:
      input_stop_loss_atr: 1.3
      input_take_profit: 2.8
      input_rsi_level_in: 42
      input_volume_spike: 1.4
      input_rsi_level_over: 70
      input_rsi_level_out: 30
      input_pull_back: 0.42
      input_initial_nbar_sl: 6

🔍 Testing EURUSD M60:
✅ โหลดพารามิเตอร์จากไฟล์: EURUSD_M60_optimization_results.json
   ✅ Found optimization file
   📊 Parameters:
      input_stop_loss_atr: 1.15
      input_take_profit: 2.3
      input_rsi_level_in: 37
      input_volume_spike: 1.28
      input_rsi_level_over: 70
      input_rsi_level_out: 33
      input_pull_back: 0.44
      input_initial_nbar_sl: 5

🔍 Testing GBPUSD M30:
✅ โหลดพารามิเตอร์จากไฟล์: EURUSD_M30_optimization_results.json
   ✅ Found optimization file
   📊 Parameters:
      input_stop_loss_atr: 1.3
      input_take_profit: 2.8
      input_rsi_level_in: 42
      input_volume_spike: 1.4
      input_rsi_level_over: 70
      input_rsi_level_out: 30
      input_pull_back: 0.42
      input_initial_nbar_sl: 6

🔍 Testing UNKNOWN M15:
✅ โหลดพารามิเตอร์จากไฟล์: EURUSD_M30_optimization_results.json
   ✅ Found optimization file
   📊 Parameters:
      input_stop_loss_atr: 1.3
      input_take_profit: 2.8
      input_rsi_level_in: 42
      input_volume_spike: 1.4
      input_rsi_level_over: 70
      input_rsi_level_out: 30
      input_pull_back: 0.42
      input_initial_nbar_sl: 6
✅ Optimization File Loading: PASSED

============================================================
🧪 Running Test: Dynamic Parameter Loading

🎯 Testing Dynamic Parameter Loading
========================================

🔍 Testing USDJPY M60:

🔄 Loading dynamic parameters for USDJPY M60
✅ โหลดพารามิเตอร์จากไฟล์: USDJPY_M60_optimization_results.json
✅ Found optimization parameters for USDJPY M60
📊 Loaded parameters for USDJPY M60:
   input_volume_spike: 1.25
   input_rsi_level_in: 35
   input_rsi_level_over: 70
   input_rsi_level_out: 35
   input_stop_loss_atr: 1.0
   input_take_profit: 2.0
   input_pull_back: 0.45
   input_initial_nbar_sl: 4
   📊 Loaded parameters:
      input_volume_spike: 1.25
      input_rsi_level_in: 35
      input_rsi_level_over: 70
      input_rsi_level_out: 35
      input_stop_loss_atr: 1.0
      input_take_profit: 2.0
      input_pull_back: 0.45
      input_initial_nbar_sl: 4

🔍 Testing GOLD M30:

🔄 Loading dynamic parameters for GOLD M30
✅ โหลดพารามิเตอร์จากไฟล์: GOLD_M30_optimization_results.json
✅ Found optimization parameters for GOLD M30
📊 Loaded parameters for GOLD M30:
   input_volume_spike: 1.3
   input_rsi_level_in: 38
   input_rsi_level_over: 70
   input_rsi_level_out: 32
   input_stop_loss_atr: 1.2
   input_take_profit: 2.5
   input_pull_back: 0.48
   input_initial_nbar_sl: 5
   📊 Loaded parameters:
      input_volume_spike: 1.3
      input_rsi_level_in: 38
      input_rsi_level_over: 70
      input_rsi_level_out: 32
      input_stop_loss_atr: 1.2
      input_take_profit: 2.5
      input_pull_back: 0.48
      input_initial_nbar_sl: 5

🔍 Testing GOLD M60:

🔄 Loading dynamic parameters for GOLD M60
✅ โหลดพารามิเตอร์จากไฟล์: GOLD_M60_optimization_results.json
✅ Found optimization parameters for GOLD M60
📊 Loaded parameters for GOLD M60:
   input_volume_spike: 1.2
   input_rsi_level_in: 36
   input_rsi_level_over: 70
   input_rsi_level_out: 34
   input_stop_loss_atr: 1.1
   input_take_profit: 2.2
   input_pull_back: 0.46
   input_initial_nbar_sl: 4
   📊 Loaded parameters:
      input_volume_spike: 1.2
      input_rsi_level_in: 36
      input_rsi_level_over: 70
      input_rsi_level_out: 34
      input_stop_loss_atr: 1.1
      input_take_profit: 2.2
      input_pull_back: 0.46
      input_initial_nbar_sl: 4

🔍 Testing EURUSD M30:

🔄 Loading dynamic parameters for EURUSD M30
✅ โหลดพารามิเตอร์จากไฟล์: EURUSD_M30_optimization_results.json
✅ Found optimization parameters for EURUSD M30
📊 Loaded parameters for EURUSD M30:
   input_volume_spike: 1.4
   input_rsi_level_in: 42
   input_rsi_level_over: 70
   input_rsi_level_out: 30
   input_stop_loss_atr: 1.3
   input_take_profit: 2.8
   input_pull_back: 0.42
   input_initial_nbar_sl: 6
   📊 Loaded parameters:
      input_volume_spike: 1.4
      input_rsi_level_in: 42
      input_rsi_level_over: 70
      input_rsi_level_out: 30
      input_stop_loss_atr: 1.3
      input_take_profit: 2.8
      input_pull_back: 0.42
      input_initial_nbar_sl: 6

🔍 Testing EURUSD M60:

🔄 Loading dynamic parameters for EURUSD M60
✅ โหลดพารามิเตอร์จากไฟล์: EURUSD_M60_optimization_results.json
✅ Found optimization parameters for EURUSD M60
📊 Loaded parameters for EURUSD M60:
   input_volume_spike: 1.28
   input_rsi_level_in: 37
   input_rsi_level_over: 70
   input_rsi_level_out: 33
   input_stop_loss_atr: 1.15
   input_take_profit: 2.3
   input_pull_back: 0.44
   input_initial_nbar_sl: 5
   📊 Loaded parameters:
      input_volume_spike: 1.28
      input_rsi_level_in: 37
      input_rsi_level_over: 70
      input_rsi_level_out: 33
      input_stop_loss_atr: 1.15
      input_take_profit: 2.3
      input_pull_back: 0.44
      input_initial_nbar_sl: 5

🔍 Testing GBPUSD M30:

🔄 Loading dynamic parameters for GBPUSD M30
✅ โหลดพารามิเตอร์จากไฟล์: EURUSD_M30_optimization_results.json
✅ Found optimization parameters for GBPUSD M30
📊 Loaded parameters for GBPUSD M30:
   input_volume_spike: 1.4
   input_rsi_level_in: 42
   input_rsi_level_over: 70
   input_rsi_level_out: 30
   input_stop_loss_atr: 1.3
   input_take_profit: 2.8
   input_pull_back: 0.42
   input_initial_nbar_sl: 6
   📊 Loaded parameters:
      input_volume_spike: 1.4
      input_rsi_level_in: 42
      input_rsi_level_over: 70
      input_rsi_level_out: 30
      input_stop_loss_atr: 1.3
      input_take_profit: 2.8
      input_pull_back: 0.42
      input_initial_nbar_sl: 6

🔍 Testing UNKNOWN M15:

🔄 Loading dynamic parameters for UNKNOWN M15
✅ โหลดพารามิเตอร์จากไฟล์: EURUSD_M30_optimization_results.json
✅ Found optimization parameters for UNKNOWN M15
📊 Loaded parameters for UNKNOWN M15:
   input_volume_spike: 1.4
   input_rsi_level_in: 42
   input_rsi_level_over: 70
   input_rsi_level_out: 30
   input_stop_loss_atr: 1.3
   input_take_profit: 2.8
   input_pull_back: 0.42
   input_initial_nbar_sl: 6
   📊 Loaded parameters:
      input_volume_spike: 1.4
      input_rsi_level_in: 42
      input_rsi_level_over: 70
      input_rsi_level_out: 30
      input_stop_loss_atr: 1.3
      input_take_profit: 2.8
      input_pull_back: 0.42
      input_initial_nbar_sl: 6
✅ Dynamic Parameter Loading: PASSED

============================================================
🧪 Running Test: Parameter Cache

💾 Testing Parameter Cache
========================================
🔄 First load (should read from file):
📋 Using cached parameters for USDJPY M60

🔄 Second load (should use cache):
📋 Using cached parameters for USDJPY M60

🧹 Clearing cache:
🗑️ Parameter cache cleared

🔄 Third load (should read from file again):

🔄 Loading dynamic parameters for USDJPY M60
✅ โหลดพารามิเตอร์จากไฟล์: USDJPY_M60_optimization_results.json
✅ Found optimization parameters for USDJPY M60
📊 Loaded parameters for USDJPY M60:
   input_volume_spike: 1.25
   input_rsi_level_in: 35
   input_rsi_level_over: 70
   input_rsi_level_out: 35
   input_stop_loss_atr: 1.0
   input_take_profit: 2.0
   input_pull_back: 0.45
   input_initial_nbar_sl: 4
✅ Cache working correctly - all parameters match
✅ Parameter Cache: PASSED

============================================================
🧪 Running Test: Multiple Symbols

🔀 Testing Multiple Symbols Simultaneously
========================================
📋 Using cached parameters for USDJPY M60
✅ Loaded USDJPY_M60

🔄 Loading dynamic parameters for GOLD M30
✅ โหลดพารามิเตอร์จากไฟล์: GOLD_M30_optimization_results.json
✅ Found optimization parameters for GOLD M30
📊 Loaded parameters for GOLD M30:
   input_volume_spike: 1.3
   input_rsi_level_in: 38
   input_rsi_level_over: 70
   input_rsi_level_out: 32
   input_stop_loss_atr: 1.2
   input_take_profit: 2.5
   input_pull_back: 0.48
   input_initial_nbar_sl: 5
✅ Loaded GOLD_M30

🔄 Loading dynamic parameters for EURUSD M60
✅ โหลดพารามิเตอร์จากไฟล์: EURUSD_M60_optimization_results.json
✅ Found optimization parameters for EURUSD M60
📊 Loaded parameters for EURUSD M60:
   input_volume_spike: 1.28
   input_rsi_level_in: 37
   input_rsi_level_over: 70
   input_rsi_level_out: 33
   input_stop_loss_atr: 1.15
   input_take_profit: 2.3
   input_pull_back: 0.44
   input_initial_nbar_sl: 5
✅ Loaded EURUSD_M60

🔄 Loading dynamic parameters for GOLD M60
✅ โหลดพารามิเตอร์จากไฟล์: GOLD_M60_optimization_results.json
✅ Found optimization parameters for GOLD M60
📊 Loaded parameters for GOLD M60:
   input_volume_spike: 1.2
   input_rsi_level_in: 36
   input_rsi_level_over: 70
   input_rsi_level_out: 34
   input_stop_loss_atr: 1.1
   input_take_profit: 2.2
   input_pull_back: 0.46
   input_initial_nbar_sl: 4
✅ Loaded GOLD_M60

🔄 Loading dynamic parameters for EURUSD M30
✅ โหลดพารามิเตอร์จากไฟล์: EURUSD_M30_optimization_results.json
✅ Found optimization parameters for EURUSD M30
📊 Loaded parameters for EURUSD M30:
   input_volume_spike: 1.4
   input_rsi_level_in: 42
   input_rsi_level_over: 70
   input_rsi_level_out: 30
   input_stop_loss_atr: 1.3
   input_take_profit: 2.8
   input_pull_back: 0.42
   input_initial_nbar_sl: 6
✅ Loaded EURUSD_M30

📊 Summary of all loaded parameters:

USDJPY_M60:
   SL ATR: 1.0
   TP Ratio: 2.0
   RSI In: 35
   Volume Spike: 1.25

GOLD_M30:
   SL ATR: 1.2
   TP Ratio: 2.5
   RSI In: 38
   Volume Spike: 1.3

EURUSD_M60:
   SL ATR: 1.15
   TP Ratio: 2.3
   RSI In: 37
   Volume Spike: 1.28

GOLD_M60:
   SL ATR: 1.1
   TP Ratio: 2.2
   RSI In: 36
   Volume Spike: 1.2

EURUSD_M30:
   SL ATR: 1.3
   TP Ratio: 2.8
   RSI In: 42
   Volume Spike: 1.4
✅ Multiple Symbols: PASSED

============================================================
🧪 Running Test: Parameter Comparison

📊 Parameter Comparison
========================================
Symbol_TF    SL_ATR   TP_Ratio  RSI_In  Vol_Spike 
--------------------------------------------------
📋 Using cached parameters for USDJPY M60
USDJPY_M60   1.0      2.0       35      1.25      
📋 Using cached parameters for GOLD M30
GOLD_M30     1.2      2.5       38      1.3       
📋 Using cached parameters for GOLD M60
GOLD_M60     1.1      2.2       36      1.2       
📋 Using cached parameters for EURUSD M30
EURUSD_M30   1.3      2.8       42      1.4       
📋 Using cached parameters for EURUSD M60
EURUSD_M60   1.15     2.3       37      1.28      
✅ Parameter Comparison: PASSED
