
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters
🛡️ เริ่มใช้งาน Model Protection System (min profit: $5,000)
🚫 เริ่มใช้งาน Training Prevention System
🔧 โหลดการตั้งค่าป้องกัน Overfitting

🛡️ ระบบป้องกันโมเดล: FLEXIBLE
   🟡 เกณฑ์ยืดหยุ่น - สมดุลระหว่างคุณภาพและการยอมรับ
   - Accuracy ≥ 45.0%
   - Win Rate ≥ 35.0%
   - Expectancy ≥ 10.0

💡 แนวคิดการบันทึกโมเดล:
   🆕 โมเดลแรก: บันทึกเสมอ (ตามเกณฑ์ของแต่ละโหมด)
   🔄 โมเดลถัดไป: เปรียบเทียบกับโมเดลก่อนหน้า

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM/Multi
   📁 Hyperparameters: LightGBM/Hyper_Multi

🏗️ Creating Multi-Model Architecture Directories:
📁 Exists: LightGBM/Data_Trained (Data Storage)
📁 Exists: LightGBM/Hyper_Multi (Hyperparameters)
📁 Exists: LightGBM/Multi_Time (Time Used Folder)
📁 Exists: LightGBM/Multi (Main Multi-Model)
📁 Exists: LightGBM/Multi/feature_importance (Feature Importance)
📁 Exists: LightGBM/Multi/individual_performance (Performance Analysis)
📁 Exists: LightGBM/Multi/models (Models Base)
📁 Exists: LightGBM/Multi/results (Results)
📁 Exists: LightGBM/Multi/thresholds (Thresholds)
📁 Exists: LightGBM/Multi/training_summaries (Training Summaries)

🏗️ เปิดใช้งาน __name__
🏗️ เปิดใช้งาน run main analysis

================================================================================
🚀 เริ่มการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-09-28 19:21:38
🔢 จำนวนรอบทั้งหมด: 1
📊 จำนวนกลุ่มข้อมูล: 1
✅ เปิดใช้งานระบบวิเคราะห์ทางการเงิน (Account Balance: $1,000.00)
📁 จำนวนไฟล์ทั้งหมด: 1
   - M60: 1 ไฟล์
================================================================================

============================================================
🔄 รอบที่ 1/1
============================================================
⏰ เวลาเริ่มรอบ: 19:21:38
📊 ประมวลผลกลุ่ม M60 (1 ไฟล์)

🏗️ เปิดใช้งาน main

🔍 ตรวจสอบการป้องกันการเทรนสำหรับ GOLD M60
⚠️ เทรนเกินจำนวนที่กำหนดแล้ววันนี้ (14/3)
🚫 ไม่สามารถเทรนได้: daily_limit_exceeded
💡 ข้ามการเทรนและใช้โมเดลเดิม

🔍 กำลังค้นหาพารามิเตอร์ที่เหมาะสมสำหรับ GOLD M60...
📁 ใช้ไฟล์: multi_asset_results_20250928_095554.json (Modified: 2025-09-28 09:55)
======================================================================
🎯 PARAMETER OPTIMIZATION RESULTS
======================================================================
📊 Source: GOLD_M60 specific

🔸 GOLD_M60
   Score: 75.48
   Win Rate: 61.9%
   Total Profit: $1,118,114
   Total Trades: 21
   Expectancy: 53243.54
   Max Drawdown: $153,914
   
   Best Parameters:
     SL ATR: 2.0
     TP Ratio: 2.5
     RSI Level: 25
     Volume Spike: 1.25
======================================================================

🔄 Updated Global Parameters:
   input_stop_loss_atr: 1.0 → 2.0
   input_take_profit: 2.0 → 2.5
   input_rsi_level_in: 35 → 25
   input_volume_spike: 1.25 (unchanged)
   input_rsi_level_over: 70 (unchanged)
   input_rsi_level_out: 30 → 35
   input_pull_back: 0.5 → 0.45
   input_initial_nbar_sl: 4 (unchanged)

============================================================
🚀 เริ่มต้นการวิเคราะห์ Multi-Model Architecture
============================================================
📋 ขั้นตอนการทำงาน:
   1. โหลดและประมวลผลข้อมูล
   2. เทรนโมเดล Multi-Model Architecture
   3. ทดสอบ Optimal Threshold
   4. ทดสอบ Optimal nBars SL
   5. บันทึกผลลัพธ์และพารามิเตอร์
============================================================
file CSV_Files_Fixed/GOLD_H1_FIXED.csv main_round 1 symbol GOLD timeframe M60

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
⚠️ ไม่พบไฟล์ threshold สำหรับ GOLD MM60, ใช้ค่า default: 0.25

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following: 2

📂 โหลดข้อมูลจาก LightGBM/Data_Trained/M60_GOLD_features.csv

🔍 ตรวจสอบ columns ข้อมูล df ขั้นตอน create_features() : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ ข้อมูลที่ส่งเข้า load_and_process_data : nBars_SL 2 confidence 0.25

🏗️ เปิดใช้งาน load and process data

🔍 กำลังตรวจสอบและจัดการ Missing Values...
✅ ไม่พบ Missing Values ในข้อมูล

🔍 กำลังสร้าง trade cycles...

🔄 Multi-Model: กำลังโหลด features จาก LightGBM/Multi/models/trend_following\M60_GOLD_features.pkl
✅ โหลดรายชื่อ Features ที่ Model ใช้สำเร็จ: 43 features
1. Hour
2. Price_Range
3. ADX_14_x_RollingVol15
4. H2_Volume_Spike
5. D1_Price_Range
6. H12_Price_Move
7. H4_MACD_signal
8. ATR_ROC_i2
9. Rolling_Close_15
10. H2_Price_Strangth
... และอีก 33 features

First 5 rows of df:
         Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0  2013.07.18  12:00:00  1281.04  1282.29  1278.50  1280.54    3360  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
1  2013.07.18  13:00:00  1280.53  1282.77  1280.18  1280.85    2748  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
2  2013.07.18  14:00:00  1281.10  1282.35  1280.29  1281.04    2718  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
3  2013.07.18  15:00:00  1281.14  1287.04  1277.57  1285.03    4864  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
4  2013.07.18  16:00:00  1285.12  1288.40  1283.80  1286.97    4524  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0

[5 rows x 332 columns]
🔎 ใช้ entry_func (Multi-Model default): trend_following

🔄 โหลดโมเดล Multi-Model Architecture สำหรับ GOLD MM60

🏗️ เปิดใช้งาน load scenario models

🔍 ตรวจสอบโมเดลที่มีอยู่สำหรับ GOLD MM60
✅ trend_following: พร้อมใช้งาน
✅ trend_following_Buy: พร้อมใช้งาน
✅ trend_following_Sell: พร้อมใช้งาน
✅ counter_trend: พร้อมใช้งาน
✅ counter_trend_Buy: พร้อมใช้งาน
✅ counter_trend_Sell: พร้อมใช้งาน

📊 สรุป: 6/6 โมเดลพร้อมใช้งาน
🔍 กำลังโหลดโมเดลสำหรับ GOLD MM60
📁 Base folder: LightGBM/Multi/models
🎯 Strategy: use_available
📋 จะโหลด 6 scenarios: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']

🔍 โหลด trend_following:
  📄 Model: LightGBM/Multi/models\trend_following\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\trend_following\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\trend_following\M60_GOLD_scaler.pkl
✅ โหลดโมเดล trend_following สำเร็จ
  📊 Features: 43 features

🔍 โหลด trend_following_Buy:
  📄 Model: LightGBM/Multi/models\trend_following_Buy\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\trend_following_Buy\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\trend_following_Buy\M60_GOLD_scaler.pkl
✅ โหลดโมเดล trend_following_Buy สำเร็จ
  📊 Features: 43 features

🔍 โหลด trend_following_Sell:
  📄 Model: LightGBM/Multi/models\trend_following_Sell\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\trend_following_Sell\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\trend_following_Sell\M60_GOLD_scaler.pkl
✅ โหลดโมเดล trend_following_Sell สำเร็จ
  📊 Features: 43 features

🔍 โหลด counter_trend:
  📄 Model: LightGBM/Multi/models\counter_trend\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend\M60_GOLD_scaler.pkl
✅ โหลดโมเดล counter_trend สำเร็จ
  📊 Features: 43 features

🔍 โหลด counter_trend_Buy:
  📄 Model: LightGBM/Multi/models\counter_trend_Buy\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend_Buy\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend_Buy\M60_GOLD_scaler.pkl
✅ โหลดโมเดล counter_trend_Buy สำเร็จ
  📊 Features: 43 features

🔍 โหลด counter_trend_Sell:
  📄 Model: LightGBM/Multi/models\counter_trend_Sell\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend_Sell\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend_Sell\M60_GOLD_scaler.pkl
✅ โหลดโมเดล counter_trend_Sell สำเร็จ
  📊 Features: 43 features

📊 สรุปการโหลดโมเดล: 6/6 โมเดล
✅ โหลดโมเดลครบทุก scenarios
✅ โหลดโมเดล Multi-Model สำเร็จ: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
⚠️ ไม่พบไฟล์ threshold สำหรับ trend_following - ใช้ค่าเริ่มต้น: 0.2500
⚠️ ไม่พบไฟล์ threshold สำหรับ counter_trend - ใช้ค่าเริ่มต้น: 0.2500
⚠️ ไม่พบไฟล์ threshold สำหรับ trend_following_Buy - ใช้ค่าเริ่มต้น: 0.2500
⚠️ ไม่พบไฟล์ threshold สำหรับ trend_following_Sell - ใช้ค่าเริ่มต้น: 0.2500
⚠️ ไม่พบไฟล์ threshold สำหรับ counter_trend_Buy - ใช้ค่าเริ่มต้น: 0.2500
⚠️ ไม่พบไฟล์ threshold สำหรับ counter_trend_Sell - ใช้ค่าเริ่มต้น: 0.2500
🎯 Scenario Thresholds: {'trend_following': 0.25, 'counter_trend': 0.25, 'trend_following_Buy': 0.25, 'trend_following_Sell': 0.25, 'counter_trend_Buy': 0.25, 'counter_trend_Sell': 0.25}

🏗️ เปิดใช้งาน try trade with threshold adjustment

🔧 เริ่มทดสอบ Multi-Model ด้วย reduce_threshold เริ่มต้น 0.8 (threshold จริง: 0.2000)

============================================================
🧪 ครั้งที่ 1: ทดสอบ reduce_threshold = 0.800 (threshold จริง: 0.2000) (Multi-Model)
============================================================
ใช้ Multi-Model พร้อม scenario_thresholds

🏗️ เปิดใช้งาน create trade cycles with multi model

🏗️ เปิดใช้งาน add market scenario column
